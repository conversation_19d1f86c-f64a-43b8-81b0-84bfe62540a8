package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// Constants for configuration
const (
	ExcelFileName    = "nuIT-Ressource Planning.xlsx"
	WorksheetName    = "Tabelle1"
	MaxSearchRows    = 100
	MaxSearchColumns = 400
	NameColumn       = "B"
	EpicColumn       = "D"
	HeaderRow        = 4
	DateFormat       = "2006-01-02"
	YearPrefix       = "20"
)

// TimesheetRecord represents a single timesheet entry
type TimesheetRecord struct {
	PK             string
	Initials       string
	Epic           string // [XXXX-XXXX]
	Year           string
	Week           string
	Startdate      string
	Enddate        string
	EstimateEffort float64 // 0.6
}

func main() {
	f, err := excelize.OpenFile(ExcelFileName)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer func(f *excelize.File) {
		err := f.Close()
		if err != nil {
			fmt.Printf("Error closing file: %v\n", err)
		}
	}(f)

	firstRow, lastRow, firstCol, lastCol := findDataBoundaries(f)
	fmt.Printf("Data found: rows %d-%d, columns %s-%s\n", firstRow, lastRow, firstCol, lastCol)

	weekHeaders := getAllWeekHeaders(f, firstCol, lastCol)
	fmt.Printf("Found %d weeks: %v\n", len(weekHeaders), weekHeaders)

	var records []TimesheetRecord

	for row := firstRow; row <= lastRow; row++ {
		name, epic := getNameAndEpic(f, row)

		if name == "" || epic == "" {
			continue
		}

		fmt.Printf("Processing %s - %s\n", name, epic)

		// Loop through each week column
		firstColNum, _ := excelize.ColumnNameToNumber(firstCol)
		for i, weekHeader := range weekHeaders {
			colNum := firstColNum + i
			colName, _ := excelize.ColumnNumberToName(colNum)

			// Get effort value from cell
			cellRef := fmt.Sprintf("%s%d", colName, row)
			cellValue, err := f.GetCellValue(WorksheetName, cellRef)

			if err != nil || cellValue == "" {
				continue // Skip empty cells
			}

			// Parse effort
			effort, err := getEstimatedEffort(cellValue)
			if err != nil {
				fmt.Printf("  Warning: Invalid effort '%s' in %s\n", cellValue, cellRef)
				continue
			}

			// Parse dates
			startDate, endDate, year, week := getDates(weekHeader)

			// Create record
			record := TimesheetRecord{
				PK:             fmt.Sprintf("%s_%s_%s_W%s", name, epic, year, week),
				Initials:       name,
				Epic:           epic,
				Year:           year,
				Week:           week,
				Startdate:      startDate.Format(DateFormat),
				Enddate:        endDate.Format(DateFormat),
				EstimateEffort: effort,
			}

			records = append(records, record)
			fmt.Printf("  Added: Week %s = %.0f%%\n", week, effort*100)
		}
	}

	// Print results
	fmt.Printf("\n=== EXTRACTION COMPLETE ===\n")
	fmt.Printf("Total records: %d\n", len(records))

	for _, record := range records {
		fmt.Printf("%s | %s | %s W%s | %.0f%%\n",
			record.Initials, record.Epic, record.Year, record.Week, record.EstimateEffort*100)
	}
}
func findDataBoundaries(f *excelize.File) (int, int, string, string) {
	firstRow := 0
	lastRow := 0
	firstCol := ""
	lastCol := ""

	// 1. Find rows with [initials] pattern in column B
	for row := 1; row <= MaxSearchRows; row++ {
		cellValue, _ := f.GetCellValue(WorksheetName, fmt.Sprintf("%s%d", NameColumn, row))

		if strings.Contains(cellValue, "[") && strings.Contains(cellValue, "]") {
			if firstRow == 0 {
				firstRow = row
				fmt.Printf("First employee row: %d (%s)\n", row, cellValue)
			}
			lastRow = row
		}
	}

	// 2. Find columns with XX-XX pattern in row 1
	for col := 1; col <= MaxSearchColumns; col++ {
		colName, _ := excelize.ColumnNumberToName(col)
		cellValue, _ := f.GetCellValue(WorksheetName, fmt.Sprintf("%s%d", colName, HeaderRow))

		if matched, _ := regexp.MatchString(`\d{2}-\d{2}`, cellValue); matched {
			if firstCol == "" {
				firstCol = colName
				fmt.Printf("First week column: %s (%s)\n", colName, cellValue)
			}
			lastCol = colName
		}
	}

	fmt.Printf("Last employee row: %d\n", lastRow)
	fmt.Printf("Last week column: %s\n", lastCol)

	return firstRow, lastRow, firstCol, lastCol
}

func extract(input string, toLower bool) string {
	// Регулярное выражение для поиска текста в квадратных скобках
	re := regexp.MustCompile(`\[([^]]+)]`)
	matches := re.FindStringSubmatch(input)

	if len(matches) > 1 {
		if toLower {
			return strings.ToLower(matches[1])
		}
		return matches[1]
	}

	return ""
}

func getNameAndEpic(f *excelize.File, row int) (string, string) {

	name, err := f.GetCellValue(WorksheetName, fmt.Sprintf("%s%d", NameColumn, row))
	if err != nil {
		fmt.Println(err)
		return "", ""
	}

	epic, err := f.GetCellValue(WorksheetName, fmt.Sprintf("%s%d", EpicColumn, row))
	if err != nil {
		fmt.Println(err)
		return "", ""
	}

	name = extract(name, true)
	epic = extract(epic, false)

	return name, epic
}

func getDates(cellValue string) (time.Time, time.Time, string, string) {

	if cellValue == "" {
		fmt.Println("Cell value is empty")
		return time.Time{}, time.Time{}, "", ""
	}

	if !strings.Contains(cellValue, "-") {
		fmt.Println("Cell value does not contain '-'")
		return time.Time{}, time.Time{}, "", ""
	}

	re := regexp.MustCompile(`^\d{2}-\d{2}$`)
	if !re.MatchString(cellValue) {
		fmt.Println("Cell value does not match expected format 'XX-XX'")
		return time.Time{}, time.Time{}, "", ""
	}

	parts := strings.Split(cellValue, "-")
	if len(parts) != 2 {
		return time.Time{}, time.Time{}, "", ""
	}

	year := fmt.Sprintf("%s%s", YearPrefix, parts[0])
	week := strings.TrimSpace(parts[1])

	yearInt, err := strconv.Atoi(year)
	if err != nil {
		fmt.Println("Error parsing year:", err)
		return time.Time{}, time.Time{}, "", ""
	}

	weekInt, err := strconv.Atoi(week)
	if err != nil {
		fmt.Println("Error parsing week:", err)
		return time.Time{}, time.Time{}, "", ""
	}

	if weekInt < 1 || weekInt > 53 {
		fmt.Printf("Invalid week number: %d. Must be between 1 and 53.\n", weekInt)
		return time.Time{}, time.Time{}, "", ""
	}

	jan1 := time.Date(yearInt, time.January, 1, 0, 0, 0, 0, time.UTC)

	weekday := jan1.Weekday()
	if weekday == time.Sunday {
		weekday = 7
	}

	daysToAdd := (weekInt-1)*7 - int(weekday) + 1
	startDate := jan1.AddDate(0, 0, daysToAdd)

	endDate := startDate.Add(7*24*time.Hour - 1*time.Second)

	fmt.Printf("Week %d of %d: %s 00:00 - %s 23:59\n", weekInt, yearInt,
		startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))

	return startDate, endDate, year, week
}

func getEstimatedEffort(cellValue string) (float64, error) {

	fmt.Println(strings.Split(cellValue, "%")[0])
	if !strings.Contains(cellValue, "%") {
		fValue, err := strconv.ParseFloat(strings.Split(cellValue, "%")[0], 64)
		if err != nil {
			return 0, fmt.Errorf("error parsing estimated effort: %w", err)
		}
		if fValue < 0 || fValue > 100 {
			return 0, fmt.Errorf("estimated effort must be between 0 and 100%%, got %f", fValue)
		}
		fmt.Printf("Estimated effort: %.2f%%\n", fValue)
		return fValue / 100, nil
	}
	fValue, err := strconv.ParseFloat(strings.Split(cellValue, "%")[0], 64)
	if err != nil {
		return 0, fmt.Errorf("error parsing estimated effort: %w", err)
	}

	fValue = fValue / 100

	if fValue < 0 || fValue > 1 {
		return 0, fmt.Errorf("estimated effort must be between 0 and 100%%, got %f", fValue)
	}

	fmt.Printf("Estimated effort: %.2f\n", fValue)

	return fValue, nil
}

func getAllWeekHeaders(f *excelize.File, firstCol, lastCol string) []string {
	var weekHeaders []string

	firstColNum, _ := excelize.ColumnNameToNumber(firstCol)
	lastColNum, _ := excelize.ColumnNameToNumber(lastCol)

	for col := firstColNum; col <= lastColNum; col++ {
		colName, _ := excelize.ColumnNumberToName(col)
		cellValue, _ := f.GetCellValue(WorksheetName, fmt.Sprintf("%s%d", colName, HeaderRow))

		if cellValue != "" {
			weekHeaders = append(weekHeaders, cellValue)
		}
	}

	return weekHeaders
}
